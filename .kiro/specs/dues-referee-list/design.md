# Design Document

## Overview

This design document outlines the implementation approach for creating a dues referee list that exactly replicates the staff management functionality. The solution focuses on reusing all existing staff components without modification, only changing the API endpoints and data source to fetch referee-specific information.

## Architecture

The referee list follows the exact same architecture as the staff section:

- **Frontend**: Reuse all existing Staff components with referee-specific service layer
- **Backend**: Create referee-specific API endpoints that mirror staff endpoints exactly
- **State Management**: Same React hooks and local state patterns as staff
- **API Communication**: New referee service that mirrors staff service structure
- **UI Components**: Zero component changes - complete reuse of staff components

## Components and Interfaces

### Component Reuse Strategy

#### 1. Direct Component Reuse (No Changes)
```typescript
// These components will be used exactly as-is:
- StaffTable
- StaffTableRow  
- StaffBankAccounts
- StaffBankTransferModal
- AddBankAccountModal
- SummaryCard
- SummaryCardSkeleton
- SearchBar
- FilterAndAddMemberControls
- ImportUsersModal
- PaymentMethodSelector
```

#### 2. New Referee Service Layer
```typescript
// New service file: pay-connect/src/services/referee.ts
export interface RefereeMember {
  id: string;
  name: string;
  email: string;
  contact: string;
  role: 'Staff' | 'Referee';
  amount: number;
  avatar: string;
}

export interface RefereeBankAccount {
  id: number;
  staffId: number; // Keep same property name for component compatibility
  staffName: string; // Keep same property name for component compatibility
  bankName: string;
  accountNumber: string;
  accountType: string;
  accountHolderName: string;
  status: 'active' | 'inactive';
  isPrimary: boolean;
  createdAt: string;
}

// Mirror all staff service functions with referee endpoints:
export const getRefereeMembers = async (filters) => {
  // Call /dues/referees instead of /staff/team
}

export const getRefereeSummary = async () => {
  // Call /dues/referees/summary instead of /staff/summary
}

export const addRefereeBankAccount = async (refereeId, bankAccountData) => {
  // Call /dues/referees/bank-account instead of /staff/bank-account
}

export const transferToRefereeBank = async (refereeId, amount, description, pin) => {
  // Call /dues/referees/transfer-to-bank instead of /staff/transfer-to-bank
}

// ... all other functions mirrored exactly
```

#### 3. New Referee Page Component
```typescript
// New page: pay-connect/src/pages/DuesReferees.tsx
import React from 'react';
import { StaffTable } from '../components/Staff/StaffTable';
import { SummaryCard } from '../components/Staff/SummaryCard';
import { SearchBar } from '../components/Staff/SearchBar';
import { FilterAndAddMemberControls } from '../components/Staff/FilterAndAddMemberControls';
import * as refereeService from '../services/referee';

const DuesReferees: React.FC = () => {
  // Use exact same state management as Staff.tsx
  // Only difference: use refereeService instead of staffService
  
  return (
    <div className="p-6">
      {/* Exact same layout as Staff page */}
      <SummaryCard data={refereeSummary} />
      <SearchBar onSearch={handleSearch} />
      <FilterAndAddMemberControls onFilter={handleFilter} />
      <StaffTable 
        data={referees} 
        onBankTransfer={handleBankTransfer}
        onAddBankAccount={handleAddBankAccount}
      />
    </div>
  );
};
```

## Data Models

### API Endpoint Mapping

#### Staff → Referee Endpoint Mapping
```typescript
// Current Staff Endpoints → New Referee Endpoints
GET /staff/team → GET /dues/referees
GET /staff/summary → GET /dues/referees/summary
POST /staff/bank-account → POST /dues/referees/bank-account
GET /staff/bank-accounts → GET /dues/referees/bank-accounts
GET /staff/bank-accounts/:id → GET /dues/referees/bank-accounts/:id
POST /staff/transfer-to-bank → POST /dues/referees/transfer-to-bank
PUT /staff/bank-accounts/:id/primary → PUT /dues/referees/bank-accounts/:id/primary
PUT /staff/bank-accounts/:id/status → PUT /dues/referees/bank-accounts/:id/status
GET /staff/staff → GET /dues/referees/importable
POST /staff/import-users → POST /dues/referees/import-users
GET /staff/departments → GET /dues/referees/departments
```

### Data Structure Compatibility

#### Referee Data Structure (Identical to Staff)
```typescript
interface RefereeMember {
  id: string;
  name: string;
  email: string;
  contact: string;
  role: 'Staff' | 'Referee'; // Same enum values for component compatibility
  amount: number;
  avatar: string;
}

interface RefereeBankAccount {
  // Exact same structure as StaffBankAccount
  id: number;
  staffId: number; // Keep same property name for component compatibility
  staffName: string; // Keep same property name for component compatibility
  bankName: string;
  accountNumber: string;
  accountType: string;
  accountHolderName: string;
  status: 'active' | 'inactive';
  isPrimary: boolean;
  createdAt: string;
}
```

## Implementation Strategy

### Phase 1: Create Referee Service Layer
1. Create `pay-connect/src/services/referee.ts`
2. Mirror all functions from `staff.ts` with referee endpoints
3. Maintain identical interfaces and return types
4. Use same error handling patterns

### Phase 2: Create Referee Page
1. Create `pay-connect/src/pages/DuesReferees.tsx`
2. Copy exact structure from `Staff.tsx`
3. Replace staff service calls with referee service calls
4. Keep all component usage identical

### Phase 3: Add Routing
1. Add route for `/dues/referees` in App.tsx
2. Add navigation link in dues section
3. Ensure proper breadcrumb navigation

### Phase 4: Backend API Implementation
1. Create referee-specific endpoints that mirror staff endpoints
2. Ensure identical request/response structures
3. Use same authentication and validation patterns
4. Maintain same error response formats

## Component Integration Points

### 1. StaffTable Component Usage
```typescript
// In DuesReferees.tsx - exact same usage as Staff.tsx
<StaffTable
  data={referees} // referee data with same structure
  loading={loading}
  onBankTransfer={handleBankTransfer} // uses referee service
  onAddBankAccount={handleAddBankAccount} // uses referee service
  onViewBankAccounts={handleViewBankAccounts} // uses referee service
  onImportUsers={handleImportUsers} // uses referee service
/>
```

### 2. Modal Component Usage
```typescript
// StaffBankTransferModal - no changes needed
<StaffBankTransferModal
  isOpen={showTransferModal}
  onClose={() => setShowTransferModal(false)}
  staffId={selectedReferee.id} // referee ID passed as staffId
  staffName={selectedReferee.name} // referee name passed as staffName
  onSuccess={handleTransferSuccess}
/>

// AddBankAccountModal - no changes needed
<AddBankAccountModal
  isOpen={showAddBankModal}
  onClose={() => setShowAddBankModal(false)}
  staffId={selectedReferee.id} // referee ID passed as staffId
  staffName={selectedReferee.name} // referee name passed as staffName
  onSuccess={handleAddBankSuccess}
/>
```

### 3. Service Layer Integration
```typescript
// In referee.ts - mirror staff service exactly
export const transferToRefereeBank = async (
  refereeId: string,
  amount: number,
  description: string,
  pin: string,
  paymentSource: 'wallet' | 'bank' = 'wallet',
  bankAccountId?: string,
  refereeBankAccountId?: string,
  paymentMethodId?: string
): Promise<{ success: boolean; message: string; transactionId?: number }> => {
  try {
    const response = await api.post('/dues/referees/transfer-to-bank', {
      staffId: refereeId, // Send as staffId for backend compatibility
      amount,
      description,
      pin,
      paymentSource,
      bankAccountId,
      staffBankAccountId: refereeBankAccountId, // Send as staffBankAccountId
      paymentMethodId
    });

    return {
      success: response.data.success,
      message: response.data.message || 'Transfer completed successfully',
      transactionId: response.data.transactionId
    };
  } catch (error) {
    // Same error handling as staff service
  }
};
```

## Error Handling

### Component Error Handling
- Use exact same error handling patterns as staff components
- No changes needed to existing error states or messages
- Same retry mechanisms and user feedback

### API Error Handling
- Mirror staff API error responses exactly
- Use same error codes and message formats
- Maintain same validation error structures

## Testing Strategy

### Component Testing
- No new component tests needed (reusing existing components)
- Test referee service functions with same patterns as staff tests
- Verify API endpoint mappings work correctly

### Integration Testing
- Test complete referee workflow matches staff workflow
- Verify all modals and actions work with referee data
- Test error scenarios match staff error handling

## Performance Considerations

### Code Reuse Benefits
- Zero additional component bundle size
- Same performance characteristics as staff section
- Leverages existing component optimizations

### API Performance
- Same caching strategies as staff endpoints
- Identical pagination and filtering performance
- Same database query optimization patterns

## Migration and Deployment

### Deployment Strategy
1. Deploy referee service layer
2. Deploy referee page component
3. Add routing configuration
4. Deploy backend API endpoints
5. Add navigation links

### Rollback Plan
- Remove referee routes if issues occur
- Referee functionality is completely separate from existing staff functionality
- No risk to existing staff management features