# Implementation Plan

- [x] 1. Create referee service layer with exact API mirroring
  - Create new file pay-connect/src/services/referee.ts
  - Copy all interfaces from staff.ts (RefereeM<PERSON>ber, RefereeBankAccount, etc.)
  - Implement getRefereeMembers function calling /dues/referees endpoint
  - Implement getRefereeSummary function calling /dues/referees/summary endpoint
  - Mirror all staff service functions with referee-specific endpoints
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5_

- [ ] 2. Implement referee bank account management functions
  - Add hasRefereeBankAccount function calling /dues/referees/bank-accounts/:id
  - Add addRefereeBankAccount function calling /dues/referees/bank-account
  - Add transferToRefereeBank function calling /dues/referees/transfer-to-bank
  - Add getRefereeBankAccounts function calling /dues/referees/bank-accounts
  - Add getRefereeBankAccountsById function calling /dues/referees/bank-accounts/:id
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5_

- [ ] 3. Implement referee bank account status management
  - Add setPrimaryBankAccount function calling /dues/referees/bank-accounts/:id/primary
  - Add updateBankAccountStatus function calling /dues/referees/bank-accounts/:id/status
  - Ensure exact same request/response structure as staff functions
  - Maintain same error handling patterns as staff service
  - _Requirements: 6.3, 6.4_

- [ ] 4. Implement referee import and department functions
  - Add getImportableReferees function calling /dues/referees/importable
  - Add importReferees function calling /dues/referees/import-users
  - Add getAvailableDepartments function calling /dues/referees/departments
  - Use same payload structure as staff import functions
  - _Requirements: 5.5, 4.1_

- [ ] 5. Create DuesReferees page component
  - Create new file pay-connect/src/pages/DuesReferees.tsx
  - Copy exact structure and state management from Staff.tsx
  - Replace all staff service calls with referee service calls
  - Import and use exact same components (StaffTable, SummaryCard, etc.)
  - Maintain identical component props and event handlers
  - _Requirements: 1.1, 1.2, 1.3, 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 6. Add referee page routing and navigation
  - Add /dues/referees route in pay-connect/src/App.tsx
  - Import DuesReferees component and add to routing configuration
  - Add navigation link in dues section to access referee list
  - Ensure proper breadcrumb navigation for referee page
  - _Requirements: 1.1_

- [ ] 7. Test component integration with referee data
  - Verify StaffTable component works with referee data structure
  - Test StaffBankTransferModal with referee IDs and names
  - Test AddBankAccountModal with referee-specific API calls
  - Verify all modals and components function identically with referee data
  - Test error handling matches staff component behavior
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [ ] 8. Implement referee search and filtering functionality
  - Verify SearchBar component works with referee data filtering
  - Test FilterAndAddMemberControls with referee role filtering
  - Ensure pagination works correctly with referee data
  - Test export functionality with referee data
  - Verify empty states display correctly for referees
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [ ] 9. Test referee bank transfer functionality
  - Test complete bank transfer workflow using StaffBankTransferModal
  - Verify transfer status tracking works with referee transfers
  - Test retry mechanisms work with referee transfer failures
  - Verify success/error messages display correctly for referee transfers
  - Test real-time status updates for referee transfers
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5_

- [ ] 10. Test referee summary statistics display
  - Verify SummaryCard component displays referee statistics correctly
  - Test summary data calculation and refresh functionality
  - Verify SummaryCardSkeleton displays during referee data loading
  - Test error states for referee summary data failures
  - Ensure summary updates after referee operations
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

- [ ] 11. Add backend API endpoints for referee functionality
  - Create GET /dues/referees endpoint mirroring /staff/team structure
  - Create GET /dues/referees/summary endpoint mirroring /staff/summary
  - Create POST /dues/referees/bank-account endpoint mirroring staff bank account
  - Create GET /dues/referees/bank-accounts endpoints mirroring staff bank accounts
  - Create POST /dues/referees/transfer-to-bank endpoint mirroring staff transfers
  - _Requirements: 3.1, 3.2, 3.3, 6.1, 7.1_

- [ ] 12. Implement referee bank account management endpoints
  - Create PUT /dues/referees/bank-accounts/:id/primary endpoint
  - Create PUT /dues/referees/bank-accounts/:id/status endpoint
  - Create GET /dues/referees/bank-accounts/:id endpoint
  - Ensure all endpoints return same data structure as staff endpoints
  - Implement same authentication and validation as staff endpoints
  - _Requirements: 6.3, 6.4, 6.5_

- [ ] 13. Add referee import and department endpoints
  - Create GET /dues/referees/importable endpoint mirroring /staff/staff
  - Create POST /dues/referees/import-users endpoint mirroring staff import
  - Create GET /dues/referees/departments endpoint mirroring staff departments
  - Use same request/response structure as staff import functionality
  - _Requirements: 5.5_

- [ ] 14. Test complete referee workflow end-to-end
  - Test referee list loading and display
  - Test referee search, filtering, and pagination
  - Test referee bank account addition and management
  - Test referee bank transfers with real payment processing
  - Test referee import functionality
  - _Requirements: 1.1, 2.1, 6.1, 7.1, 5.5_

- [ ] 15. Verify component reuse and consistency
  - Confirm no modifications needed to existing staff components
  - Verify identical behavior between staff and referee functionality
  - Test that all row actions work identically for referees
  - Ensure consistent error handling and user feedback
  - Validate same performance characteristics as staff section
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_