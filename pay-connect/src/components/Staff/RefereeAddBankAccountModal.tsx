import React, { useState, useEffect } from 'react';
import { X, CreditCard, AlertCircle, RefreshCw } from 'lucide-react';
import { addRefereeBankAccount, BankAccountData, hasRefereeBankAccount, RefereeBankAccount } from '../../services/referee';

interface RefereeAddBankAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  refereeId: string;
  refereeName: string;
  onSuccess: () => void;
  isReplacement?: boolean;
  existingAccount?: RefereeBankAccount;
}

const RefereeAddBankAccountModal: React.FC<RefereeAddBankAccountModalProps> = ({
  isOpen,
  onClose,
  refereeId,
  refereeName,
  onSuccess,
  isReplacement = false,
  existingAccount
}) => {
  const [formData, setFormData] = useState<BankAccountData>({
    bankName: '',
    accountNumber: '',
    routingNumber: '',
    accountType: 'checking',
    accountHolderName: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{
    bankName?: string;
    accountNumber?: string;
    routingNumber?: string;
    accountHolderName?: string;
  }>({});

  // Validation functions
  const validateBankName = (name: string): string | undefined => {
    if (!name.trim()) {
      return "Bank name is required";
    }
    if (name.trim().length < 2) {
      return "Bank name is too short";
    }
    return undefined;
  };

  const validateAccountNumber = (accountNumber: string): string | undefined => {
    const cleaned = accountNumber.replace(/[\s-]/g, '');
    if (!cleaned) {
      return "Account number is required";
    }
    if (cleaned.length < 8 || cleaned.length > 17) {
      return "Account number must be between 8 and 17 digits";
    }
    if (!/^\d+$/.test(cleaned)) {
      return "Account number must contain only digits";
    }
    return undefined;
  };

  const validateRoutingNumber = (routingNumber: string): string | undefined => {
    const cleaned = routingNumber.replace(/[\s-]/g, '');
    if (!cleaned) {
      return "Routing number is required";
    }
    if (cleaned.length !== 9) {
      return "Routing number must be exactly 9 digits";
    }
    if (!/^\d+$/.test(cleaned)) {
      return "Routing number must contain only digits";
    }
    return undefined;
  };

  const validateAccountHolderName = (name: string): string | undefined => {
    if (!name.trim()) {
      return "Account holder name is required";
    }
    if (name.trim().length < 2) {
      return "Account holder name is too short";
    }
    return undefined;
  };

  const [submitStatus, setSubmitStatus] = useState<{
    type: 'success' | 'error' | null;
    message: string;
  }>({ type: null, message: '' });

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        bankName: existingAccount?.bankName || '',
        accountNumber: existingAccount?.accountNumber || '',
        routingNumber: '',
        accountType: (existingAccount?.accountType as 'checking' | 'savings') || 'checking',
        accountHolderName: existingAccount?.accountHolderName || refereeName
      });
      setErrors({});
      setSubmitStatus({ type: null, message: '' });
    }
  }, [isOpen, existingAccount, refereeName]);

  // Check for existing account when component mounts
  useEffect(() => {
    const checkExistingAccount = async () => {
      if (refereeId && isOpen) {
        try {
          const { hasAccount, account } = await hasRefereeBankAccount(refereeId);
          if (hasAccount && account && !isReplacement) {
            setSubmitStatus({
              type: 'error',
              message: 'Referee member already has a bank account. Please use the update account function instead.'
            });
          }
        } catch (error) {
          console.error('Error checking existing account:', error);
        }
      }
    };

    checkExistingAccount();
  }, [refereeId, isOpen, isReplacement]);

  const handleInputChange = (field: keyof BankAccountData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
    
    // Clear submit status when user makes changes
    if (submitStatus.type) {
      setSubmitStatus({ type: null, message: '' });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: typeof errors = {};
    
    newErrors.bankName = validateBankName(formData.bankName);
    newErrors.accountNumber = validateAccountNumber(formData.accountNumber);
    newErrors.routingNumber = validateRoutingNumber(formData.routingNumber);
    newErrors.accountHolderName = validateAccountHolderName(formData.accountHolderName);
    
    setErrors(newErrors);
    
    return !Object.values(newErrors).some(error => error !== undefined);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    setSubmitStatus({ type: null, message: '' });
    
    try {
      // Clean the form data before sending
      const cleanedFormData = {
        ...formData,
        bankName: formData.bankName.trim(),
        accountHolderName: formData.accountHolderName.trim(),
        accountNumber: formData.accountNumber.replace(/[\s-]/g, ''),
        routingNumber: formData.routingNumber.replace(/[\s-]/g, '')
      };
      
      // Pass isReplacement flag to the API
      const result = await addRefereeBankAccount(refereeId, cleanedFormData, isReplacement);
      
      if (result.success) {
        // Show success message
        setSubmitStatus({
          type: 'success',
          message: result.message || 'Bank account added successfully!'
        });
        
        // Call onSuccess after a short delay to show the success message
        setTimeout(() => {
          onSuccess();
          onClose();
        }, 1500);
      } else {
        setSubmitStatus({
          type: 'error',
          message: result.message || 'Failed to add bank account. Please try again.'
        });
      }
    } catch (error) {
      console.error('Error adding bank account:', error);
      setSubmitStatus({
        type: 'error',
        message: 'An unexpected error occurred. Please try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <CreditCard className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {isReplacement ? 'Update Bank Account' : 'Add Bank Account'}
              </h2>
              <p className="text-sm text-gray-600">For {refereeName}</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Status Message */}
          {submitStatus.type && (
            <div className={`p-4 rounded-lg flex items-start space-x-3 ${
              submitStatus.type === 'success' 
                ? 'bg-green-50 border border-green-200' 
                : 'bg-red-50 border border-red-200'
            }`}>
              <AlertCircle className={`h-5 w-5 mt-0.5 ${
                submitStatus.type === 'success' ? 'text-green-600' : 'text-red-600'
              }`} />
              <div className="flex-1">
                <p className={`text-sm font-medium ${
                  submitStatus.type === 'success' ? 'text-green-800' : 'text-red-800'
                }`}>
                  {submitStatus.message}
                </p>
              </div>
            </div>
          )}

          {/* Bank Name */}
          <div>
            <label htmlFor="bankName" className="block text-sm font-medium text-gray-700 mb-1">
              Bank Name *
            </label>
            <input
              type="text"
              id="bankName"
              value={formData.bankName}
              onChange={(e) => handleInputChange('bankName', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.bankName ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter bank name"
              disabled={isSubmitting}
            />
            {errors.bankName && (
              <p className="mt-1 text-sm text-red-600">{errors.bankName}</p>
            )}
          </div>

          {/* Account Holder Name */}
          <div>
            <label htmlFor="accountHolderName" className="block text-sm font-medium text-gray-700 mb-1">
              Account Holder Name *
            </label>
            <input
              type="text"
              id="accountHolderName"
              value={formData.accountHolderName}
              onChange={(e) => handleInputChange('accountHolderName', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.accountHolderName ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter account holder name"
              disabled={isSubmitting}
            />
            {errors.accountHolderName && (
              <p className="mt-1 text-sm text-red-600">{errors.accountHolderName}</p>
            )}
          </div>

          {/* Account Type */}
          <div>
            <label htmlFor="accountType" className="block text-sm font-medium text-gray-700 mb-1">
              Account Type *
            </label>
            <select
              id="accountType"
              value={formData.accountType}
              onChange={(e) => handleInputChange('accountType', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              disabled={isSubmitting}
            >
              <option value="checking">Checking</option>
              <option value="savings">Savings</option>
            </select>
          </div>

          {/* Account Number */}
          <div>
            <label htmlFor="accountNumber" className="block text-sm font-medium text-gray-700 mb-1">
              Account Number *
            </label>
            <input
              type="text"
              id="accountNumber"
              value={formData.accountNumber}
              onChange={(e) => handleInputChange('accountNumber', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.accountNumber ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter account number"
              disabled={isSubmitting}
            />
            {errors.accountNumber && (
              <p className="mt-1 text-sm text-red-600">{errors.accountNumber}</p>
            )}
          </div>

          {/* Routing Number */}
          <div>
            <label htmlFor="routingNumber" className="block text-sm font-medium text-gray-700 mb-1">
              Routing Number *
            </label>
            <input
              type="text"
              id="routingNumber"
              value={formData.routingNumber}
              onChange={(e) => handleInputChange('routingNumber', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.routingNumber ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter routing number"
              disabled={isSubmitting}
            />
            {errors.routingNumber && (
              <p className="mt-1 text-sm text-red-600">{errors.routingNumber}</p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || submitStatus.type === 'success'}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center justify-center space-x-2"
            >
              {isSubmitting ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <span>Adding...</span>
                </>
              ) : (
                <span>{isReplacement ? 'Update Account' : 'Add Account'}</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RefereeAddBankAccountModal;
