import React, { useState, useEffect } from 'react';
import StaffTableRow from './StaffTableRow';
import { getRefereeMembers, RefereeMember } from '../../services/referee';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import EmptyState from '../common/EmptyState';
import PaymentLoader from '../common/PaymentLoader';
import RefereeAddBankAccountModal from './RefereeAddBankAccountModal';
import StaffBankTransferModal from './StaffBankTransferModal';
// TODO: Create RefereeStaffBankTransferModal for referee-specific transfers

interface RefereeTableProps {
  className?: string;
  search: string;
  roleFilter: string;
  onDelete: (id: string) => void;
}

const RefereeTable: React.FC<RefereeTableProps> = ({
  className,
  search,
  roleFilter,
  onDelete,
}) => {
  const [refereeMembers, setRefereeMembers] = useState<RefereeMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showAddBankModal, setShowAddBankModal] = useState(false);
  const [showTransferModal, setShowTransferModal] = useState(false);

  const [selectedRefereeForBank, setSelectedRefereeForBank] = useState<RefereeMember | null>(null);
  const [selectedRefereeForTransfer, setSelectedRefereeForTransfer] = useState<RefereeMember | null>(null);

  const handleAddBankAccount = (referee: RefereeMember) => {
    setSelectedRefereeForBank(referee);
    setShowAddBankModal(true);
  };

  const handleBankTransfer = (referee: RefereeMember) => {
    setSelectedRefereeForTransfer(referee);
    setShowTransferModal(true);
  };

  const handleBankSuccess = () => {
    // Refresh referee data after bank operations
    refreshData();
    setSelectedRefereeForBank(null);
    setSelectedRefereeForTransfer(null);
  };

  useEffect(() => {
    fetchRefereeMembers();
  }, [search, roleFilter, currentPage]);

  const fetchRefereeMembers = async () => {
    try {
      setLoading(true);
      
      // Only add a small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Fetch referee members with pagination and filtering
      const { data, totalPages: newTotalPages } = await getRefereeMembers({
        search,
        role: roleFilter !== 'All' ? (roleFilter as RefereeMember['role']) : undefined,
        page: currentPage,
        limit: 10
      });
      
      setRefereeMembers(data);
      setTotalPages(newTotalPages);
      setError(null);
    } catch (err) {
      console.error('Error fetching referee members:', err);
      setError('Failed to fetch referee members. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Function to refresh data after operations
  const refreshData = () => {
    fetchRefereeMembers();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <PaymentLoader
          type="setup"
          message="Loading referee members..."
          size="small"
          showQuotes={true}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="text-red-500 text-lg font-medium mb-2">Error</div>
          <div className="text-gray-600 mb-4">{error}</div>
          <button
            onClick={fetchRefereeMembers}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (refereeMembers.length === 0) {
    return (
      <EmptyState
        title="No referee members found"
        description="No referee members match your current search and filter criteria."
        actionText="Clear Filters"
        onAction={() => {
          // This would typically clear filters in the parent component
          console.log('Clear filters clicked');
        }}
      />
    );
  }

  const startIndex = (currentPage - 1) * 10;
  const endIndex = Math.min(startIndex + 10, refereeMembers.length);
  const currentRefereeMembers = refereeMembers.slice(0, 10); // Since we're already paginating in the API

  return (
    <div className={`bg-white ${className}`}>
      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-900">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Email
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Contact
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Amount
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Role
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {currentRefereeMembers.map((referee) => (
              <StaffTableRow 
                key={referee.id} 
                staff={referee} // Pass referee as staff for component compatibility
                onDelete={onDelete}
                onAddBankAccount={handleAddBankAccount}
                onBankTransfer={handleBankTransfer}
              />
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                <span className="font-medium">{endIndex}</span> of{' '}
                <span className="font-medium">{refereeMembers.length}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Previous</span>
                  <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                </button>
                
                {/* Page numbers */}
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                      page === currentPage
                        ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                ))}
                
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Next</span>
                  <ChevronRight className="h-5 w-5" aria-hidden="true" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Modals */}
      {selectedRefereeForBank && (
        <RefereeAddBankAccountModal
          isOpen={showAddBankModal}
          onClose={() => {
            setShowAddBankModal(false);
            setSelectedRefereeForBank(null);
          }}
          refereeId={selectedRefereeForBank.id}
          refereeName={selectedRefereeForBank.name}
          onSuccess={handleBankSuccess}
        />
      )}

      {selectedRefereeForTransfer && (
        <StaffBankTransferModal
          isOpen={showTransferModal}
          onClose={() => {
            setShowTransferModal(false);
            setSelectedRefereeForTransfer(null);
          }}
          staff={selectedRefereeForTransfer} // Pass referee as staff for component compatibility
          onSuccess={handleBankSuccess}
        />
      )}
    </div>
  );
};

export default RefereeTable;
