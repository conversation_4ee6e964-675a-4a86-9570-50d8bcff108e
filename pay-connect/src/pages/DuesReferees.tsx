import React, { useState, useEffect } from 'react';
import SummaryCard from '../components/Staff/SummaryCard';
import SummaryCardSkeleton from '../components/Staff/SummaryCardSkeleton';
import SearchBar from '../components/Staff/SearchBar';
import FilterAndAddMemberControls from '../components/Staff/FilterAndAddMemberControls';
import RefereeTable from '../components/Staff/RefereeTable';
import StaffBankAccounts from '../components/Staff/StaffBankAccounts';
import ImportUsersModal from '../components/Staff/ImportUsersModal';
import { getRefereeSummary, RefereeMember } from '../services/referee';
import PaymentLoader from '../components/common/PaymentLoader';
import DeleteConfirmModal from '../components/Staff/DeleteConfirmModal';
import AddBankAccountModal from '../components/Staff/AddBankAccountModal';
import StaffBankTransferModal from '../components/Staff/StaffBankTransferModal';

const DuesReferees: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'staff' | 'bank-accounts'>('staff');
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('All');
  const [selectedCard, setSelectedCard] = useState<string | null>(null);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedRefereeId, setSelectedRefereeId] = useState<string | null>(null);
  const [summary, setSummary] = useState({
    totalStaff: 0,
    Staff: 0,
    referee: 0,
    totalAmountDue: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSummary();
  }, []);

  const fetchSummary = async () => {
    try {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 2000));
      const data = await getRefereeSummary();
      setSummary(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch referee summary');
    } finally {
      setLoading(false);
    }
  };

  const handleImportUsers = () => {
    setIsImportModalOpen(true);
  };

  const handleImportSuccess = () => {
    fetchSummary();
  };

  const handleCardClick = (cardName: string, role?: string) => {
    setSelectedCard(cardName);
    setRoleFilter(role || 'All');
  };

  const handleDeleteRefereeClick = (id: string) => {
    setSelectedRefereeId(id);
    setShowDeleteModal(true);
  };

  const confirmDeleteReferee = () => {
    if (!selectedRefereeId) return;

    alert(`Referee member with ID ${selectedRefereeId} would be deleted (functionality not implemented in this demo).`);

    setSelectedRefereeId(null);
    setShowDeleteModal(false);
  };

  return (
    <div className="space-y-6">
      {/* Header with Tabs */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Referee Management</h1>
            <p className="text-gray-600">Manage your referee members and their bank accounts</p>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('staff')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'staff'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
            >
              Referee Members
            </button>
            <button
              onClick={() => setActiveTab('bank-accounts')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'bank-accounts'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
            >
              Bank Accounts
            </button>
          </nav>
        </div>
      </div>

      {activeTab === 'staff' && (
        <>
          {/* Summary Cards */}
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <SummaryCardSkeleton />
              <SummaryCardSkeleton />
              <SummaryCardSkeleton />
              <SummaryCardSkeleton />
            </div>
          ) : error ? (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <SummaryCard
                title="Total Referees"
                value={summary.totalStaff}
                icon="users"
                selected={selectedCard === 'Total Referees'}
                onClick={() => handleCardClick('Total Referees')}
              />
              <SummaryCard
                title="Staff"
                value={summary.Staff}
                icon="user"
                selected={selectedCard === 'Staff'}
                onClick={() => handleCardClick('Staff')}
              />
              <SummaryCard
                title="Referee"
                value={summary.referee}
                icon="shield"
                selected={selectedCard === 'Referee'}
                onClick={() => handleCardClick('Referee')}
              />
              <SummaryCard
                title="Total Amount Due"
                value={summary.totalAmountDue}
                icon="dollar"
                selected={selectedCard === 'Total Amount Due'}
                onClick={() => handleCardClick('Total Amount Due')}
              />
            </div>

          )}

          {/* Search and Controls */}
          <div className="bg-white rounded-xl shadow-sm border p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 justify-between gap-5">
              <div className="col-span-1">
                <SearchBar
                  searchTerm={searchTerm}
                  onSearchChange={setSearchTerm}
                  onSearch={(term) => console.log('Searching for:', term)}
                />
              </div>
              <div className="col-span-1">
                <FilterAndAddMemberControls
                  currentFilter={roleFilter}
                  onFilterChange={setRoleFilter}
                  onImportUsers={handleImportUsers}
                />
              </div>
            </div>
          </div>


          {/* Referee Table */}
          <div className='border rounded-lg overflow-hidden'>
            <RefereeTable
              search={searchTerm}
              roleFilter={roleFilter}
              onDelete={handleDeleteRefereeClick}
            />
          </div>
        </>
      )}

      {activeTab === 'bank-accounts' && <StaffBankAccounts />}

      {/* Modals */}
      <div>
        <ImportUsersModal
          isOpen={isImportModalOpen}
          onClose={() => setIsImportModalOpen(false)}
          onImportSuccess={handleImportSuccess}
        />

        <DeleteConfirmModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={confirmDeleteReferee}
          staffName={selectedRefereeId || ''}
        />
      </div>
    </div>
  );
};

export default DuesReferees;
