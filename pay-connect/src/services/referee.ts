import api from './api';

export interface RefereeMember {
  id: string;
  name: string;
  email: string;
  contact: string;
  role: 'Staff' | 'Referee';
  amount: number;
  avatar: string; // URL to an avatar image
}

export interface ImportableReferee {
  id: string;
  added_by: number;
  name: string;
  email: string;
  contact: string;
  department: string;
  position: string;
  avatar: string;
  isSelected?: boolean;
}

export interface RefereeBankAccount {
  id: number;
  staffId: number; // Keep same property name for component compatibility
  staffName: string; // Keep same property name for component compatibility
  bankName: string;
  accountNumber: string;
  accountType: string;
  accountHolderName: string;
  status: 'active' | 'inactive';
  isPrimary: boolean;
  createdAt: string;
}

export interface BankAccountData {
  bankName: string;
  accountNumber: string;
  routingNumber: string;
  accountType: 'checking' | 'savings';
  accountHolderName: string;
}

export const getRefereeMembers = async (filters: { role?: string, search?: string, page?: number, limit?: number }): Promise<{ data: RefereeMember[], totalPages: number }> => {
  try {
    const response = await api.get(`/dues/referees`);
    let filteredData = response.data.data || response.data;

    if (filters.role && filters.role !== 'All') {
      filteredData = filteredData.filter((referee: RefereeMember) => referee.role === filters.role);
    }

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredData = filteredData.filter((referee: RefereeMember) =>
        referee.name.toLowerCase().includes(searchTerm) ||
        referee.email.toLowerCase().includes(searchTerm) ||
        referee.contact.toLowerCase().includes(searchTerm)
      );
    }

    const limit = filters.limit || 10;
    const totalPages = Math.ceil(filteredData.length / limit);
    
    // Apply pagination if requested
    if (filters.page && filters.limit) {
      const startIndex = (filters.page - 1) * filters.limit;
      filteredData = filteredData.slice(startIndex, startIndex + filters.limit);
    }
    
    return { data: filteredData, totalPages };
  } catch (error) {
    console.error('Error fetching referee members:', error);
    throw error;
  }
};

export const getRefereeSummary = async () => {
  const { data: refereeMembers } = await getRefereeMembers({});
  return {
    totalStaff: refereeMembers.length,
    referee: refereeMembers.filter(referee => referee.role === 'Referee').length,
    Staff: refereeMembers.filter(referee => referee.role === 'Staff').length || 0,
    totalAmountDue: refereeMembers.reduce((sum, referee) => sum + referee.amount, 0)
  };
};

// Get importable referees from external database
export const getImportableReferees = async (filters: { department?: string, search?: string }): Promise<ImportableReferee[]> => {
  try {
    const params = new URLSearchParams();

    if (filters.department && filters.department !== 'All') {
      params.append('department', filters.department);
    }

    if (filters.search) {
      params.append('search', filters.search);
    }

    const response = await api.get(`/dues/referees/importable?${params.toString()}`);

    return response.data.data || response.data;
  } catch (error) {
    console.error('Error fetching importable referees:', error);
    throw error; // Re-throw the error instead of falling back to mock data
  }
};

// Import selected users as referee members
export const importReferees = async (selectedUsers: ImportableReferee[], defaultRole: 'Staff' | 'Referee'): Promise<{ success: boolean; imported: number; message: string }> => {
  try {
    const payload = {
      users: selectedUsers.map(user => ({
        id: user.id,
        added_by: user.added_by,
        name: user.name,
        email: user.email,
        contact: user.contact,
        department: user.department,
        position: user.position,
        avatar: user.avatar,
        role: defaultRole
      })),
      defaultRole
    };

    const response = await api.post('/dues/referees/import-users', payload);

    const result = response.data;

    return {
      success: result.success || true,
      imported: result.imported || selectedUsers.length,
      message: result.message || `Successfully imported ${selectedUsers.length} user${selectedUsers.length !== 1 ? 's' : ''} as referee members.`
    };
  } catch (error) {
    console.error('Error importing referees:', error);

    let errorMessage = 'Failed to import referees. Please try again.';

    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: { message?: string } } };
      errorMessage = axiosError.response?.data?.message || errorMessage;
    }

    return {
      success: false,
      imported: 0,
      message: errorMessage
    };
  }
};

// Get available departments for filtering
export const getAvailableRefereeDepartments = async (): Promise<string[]> => {
  try {
    const response = await api.get('/dues/referees/departments');
    return response.data.data || response.data;
  } catch (error) {
    console.error('Error fetching referee departments:', error);
    // Return default departments if API fails
    return ['Administration', 'Finance', 'Operations', 'Sports Management'];
  }
};

// Check if referee member already has a bank account
export const hasRefereeBankAccount = async (
  refereeId: string
): Promise<{ hasAccount: boolean; account?: RefereeBankAccount }> => {
  try {
    const accounts = await getRefereeBankAccountsById(refereeId);
    const activeAccount = accounts.find(account => account.status === 'active');

    return {
      hasAccount: !!activeAccount,
      account: activeAccount
    };
  } catch (error) {
    console.error('Error checking referee bank account:', error);
    return { hasAccount: false };
  }
};

// Add referee bank account
export const addRefereeBankAccount = async (
  refereeId: string,
  bankAccountData: BankAccountData,
  isReplacement: boolean = false
): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await api.post('/dues/referees/bank-account', {
      staffId: refereeId, // Send as staffId for backend compatibility
      bankAccountData,
      isReplacement
    });

    return {
      success: response.data.success,
      message: response.data.message || (isReplacement ? 'Bank account replaced successfully' : 'Bank account added successfully')
    };
  } catch (error) {
    console.error('Error adding referee bank account:', error);
    return {
      success: false,
      message: 'Failed to add bank account'
    };
  }
};

// Transfer to referee bank
export const transferToRefereeBank = async (
  refereeId: string,
  amount: number,
  description: string,
  pin: string, // PIN is now required
  paymentSource: 'wallet' | 'bank' = 'wallet',
  bankAccountId?: string,
  refereeBankAccountId?: string,
  paymentMethodId?: string // New parameter for payment method
): Promise<{ success: boolean; message: string; transactionId?: number }> => {
  try {
    const response = await api.post('/dues/referees/transfer-to-bank', {
      staffId: refereeId, // Send as staffId for backend compatibility
      amount,
      description,
      pin,
      paymentSource,
      bankAccountId,
      staffBankAccountId: refereeBankAccountId, // Send as staffBankAccountId for backend compatibility
      paymentMethodId // Include payment method ID in the request
    });

    return {
      success: response.data.success,
      message: response.data.message || 'Transfer completed successfully',
      transactionId: response.data.transactionId
    };
  } catch (error) {
    console.error('Error transferring to referee bank:', error);

    let errorMessage = 'Failed to transfer funds to bank account';
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: { message?: string } } };
      errorMessage = axiosError.response?.data?.message || errorMessage;
    }

    return {
      success: false,
      message: errorMessage
    };
  }
};

// Get all referee bank accounts
export const getRefereeBankAccounts = async (): Promise<RefereeBankAccount[]> => {
  try {
    const response = await api.get('/dues/referees/bank-accounts');
    const accounts = response.data.data?.accounts || [];

    // Process accounts to identify primary accounts for each referee member
    const refereeAccounts = new Map<string, RefereeBankAccount[]>();

    accounts.forEach((account: RefereeBankAccount) => {
      const refereeId = account.staffId;
      if (!refereeAccounts.has(refereeId)) {
        refereeAccounts.set(refereeId, []);
      }
      refereeAccounts.get(refereeId)?.push(account);
    });

    // Mark the first account for each referee as primary if no primary is set
    const enhancedAccounts: RefereeBankAccount[] = [];
    refereeAccounts.forEach(refereeAccountList => {
      // Check if any account is already marked as primary
      const hasPrimary = refereeAccountList.some(account => account.isPrimary);

      if (!hasPrimary && refereeAccountList.length > 0) {
        refereeAccountList[0].isPrimary = true;
      }
      enhancedAccounts.push(...refereeAccountList);
    });

    return enhancedAccounts;
  } catch (error) {
    console.error('Error fetching referee bank accounts:', error);
    return [];
  }
};

// Get bank accounts for a specific referee member
export const getRefereeBankAccountsById = async (refereeId: string): Promise<RefereeBankAccount[]> => {
  try {
    const response = await api.get(`/dues/referees/bank-accounts/${refereeId}`);
    return response.data.data.accounts || [];
  } catch (error) {
    console.error('Error fetching referee bank accounts:', error);
    throw error;
  }
};

// Set a bank account as primary
export const setPrimaryRefereeBankAccount = async (
  refereeId: string,
  accountId: number
): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await api.put(`/dues/referees/bank-accounts/${refereeId}/primary`, {
      accountId
    });

    return {
      success: response.data.success || true,
      message: response.data.message || 'Primary bank account updated successfully'
    };
  } catch (error) {
    console.error('Error setting primary referee bank account:', error);

    let errorMessage = 'Failed to set primary bank account';
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: { message?: string } } };
      errorMessage = axiosError.response?.data?.message || errorMessage;
    }

    return {
      success: false,
      message: errorMessage
    };
  }
};

// Update bank account status (activate/deactivate)
export const updateRefereeBankAccountStatus = async (
  accountId: number,
  status: 'active' | 'inactive'
): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await api.put(`/dues/referees/bank-accounts/${accountId}/status`, {
      status
    });

    return {
      success: response.data.success || true,
      message: response.data.message || `Bank account ${status === 'active' ? 'activated' : 'deactivated'} successfully`
    };
  } catch (error) {
    console.error('Error updating referee bank account status:', error);

    let errorMessage = `Failed to ${status === 'active' ? 'activate' : 'deactivate'} bank account`;
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: { message?: string } } };
      errorMessage = axiosError.response?.data?.message || errorMessage;
    }

    return {
      success: false,
      message: errorMessage
    };
  }
};

// Referee transaction history interfaces and functions
export interface RefereeTransactionFilters {
  limit?: number;
  offset?: number;
  search?: string;
  type?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface RefereeTransactionResponse {
  transactions: any[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
    currentPage: number;
    totalPages: number;
  };
  filters: RefereeTransactionFilters;
}

export const getRefereeTransactions = async (
  userId: string,
  filters: RefereeTransactionFilters = {}
): Promise<RefereeTransactionResponse> => {
  try {
    console.log('getRefereeTransactions called with:', { userId, filters });

    const params = new URLSearchParams();
    params.append('userId', userId);

    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.offset) params.append('offset', filters.offset.toString());
    if (filters.search) params.append('search', filters.search);
    if (filters.type) params.append('type', filters.type);
    if (filters.status) params.append('status', filters.status);
    if (filters.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters.dateTo) params.append('dateTo', filters.dateTo);

    const url = `/dues/referees/transactions?${params.toString()}`;
    console.log('Making API request to:', url);

    const response = await api.get(url);

    if (response.data && response.data.success) {
      return response.data.data;
    }

    // Return empty response if API fails
    return {
      transactions: [],
      pagination: {
        total: 0,
        limit: filters.limit || 20,
        offset: filters.offset || 0,
        hasMore: false,
        currentPage: 1,
        totalPages: 0
      },
      filters
    };
  } catch (error) {
    console.error('Error fetching referee transactions:', error);

    // Return empty response on error
    return {
      transactions: [],
      pagination: {
        total: 0,
        limit: filters.limit || 20,
        offset: filters.offset || 0,
        hasMore: false,
        currentPage: 1,
        totalPages: 0
      },
      filters
    };
  }
};

// Get referee details (similar to staff details)
export interface RefereeDetail {
  id: string;
  full_name: string;
  email: string;
  phone: string;
  location: string;
  role: string;
  profile_pic: string;
  totalPaymentPaid: number;
  lastPaidDate: string;
  upcomingPayment: number;
  paymentDate: string;
  overduePayment: number;
  gameActivityCount: number;
  transactionsCount: number;
  transactions: any[];
}

export const getRefereeDetails = async (id: string): Promise<RefereeDetail> => {
  try {
    const response = await api.get(`/dues/referees/details?id=${id}`);
    return response.data.data;
  } catch (error) {
    console.error('Error fetching referee details:', error);
    throw error;
  }
};
