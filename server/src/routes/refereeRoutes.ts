import express from 'express';
import {
  getUserReferees,
  getRefereeDetailsInfo,
  getRefereeDepartmentInfo,
  getRefereeManagerInfo,
  getRefereeTeam,
  getRefereeProjectsInfo,
  getRefereePermissionsInfo,
  getComprehensiveRefereeData,
  importReferees,
  getRefereeTransactions,
  addRefereeBankAccount,
  getRefereeBankAccounts,
  transferToRefereeBank,
  getSingleRefereeBankAccounts,
  updateRefereeBankAccount,
  syncRefereeTransferStatus,
  getRefereeTransferStatus
} from '../controllers/refereeController';
import { authenticateTokenAndSession } from '../middlewares/sessionAuth';

const router = express.Router();

// All referee routes require full authentication (JWT + session)
router.use(authenticateTokenAndSession);

// Referee and user information routes (using secondary database)
router.get('/referees', getUserReferees);                    // Get user's referee members
router.get('/details', getRefereeDetailsInfo);              // Get referee details
router.get('/department', getRefereeDepartmentInfo);        // Get referee's department
router.get('/manager', getRefereeManagerInfo);              // Get referee's manager
router.get('/team', getRefereeTeam);                        // Get referee's team members
router.get('/projects', getRefereeProjectsInfo);            // Get referee's projects
router.get('/permissions', getRefereePermissionsInfo);      // Get referee's permissions
router.get('/comprehensive', getComprehensiveRefereeData);   // Get all referee info

// Referee management routes
router.post('/import-users', importReferees);               // Import multiple users as referees

// Transaction routes
router.get('/transactions', getRefereeTransactions);        // Get referee member transactions with pagination

// Referee bank account routes
router.post('/bank-account', addRefereeBankAccount);         // Add referee bank account
router.get('/bank-accounts', getRefereeBankAccounts);        // Get all referee bank accounts
router.get('/bank-accounts/:staffId', getSingleRefereeBankAccounts); // Get specific referee bank account (keep :staffId for compatibility)
router.put('/bank-accounts/:accountId', updateRefereeBankAccount); // Update referee bank account
router.post('/transfer-to-bank', transferToRefereeBank);     // Transfer to referee bank account

// Transfer status routes
router.post('/sync-transfer-status', syncRefereeTransferStatus); // Sync referee transfer statuses with Plaid
router.get('/transfer-status/:transferId', getRefereeTransferStatus); // Get individual referee transfer status

export default router;
