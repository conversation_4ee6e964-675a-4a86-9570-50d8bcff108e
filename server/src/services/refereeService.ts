// import { RefereeDetails } from './refereeService';
import { comparePin } from '../models/user';
import { executeQuery, executeQuerySingle, executeSecondaryQuery, executeSecondaryQuerySingle, executeUpdate, recordExists } from '../utils/database';
import logger from '../utils/logger';
import { createWalletTransaction, getUserMasterWallet } from './walletService';

export interface RefereeMember {
  id: number;
  added_by: number;
  user_id: number;
  name: string;
  email: string;
  contact: string;
  role: string;
  department: string;
  status: string;
  avatar: string;
  created_at: Date;
  updated_at: Date;
}

export interface RefereeDetails {
  id: number;
  name: string;
  contact: string;
  department?: string;
  position?: string;
  avatar?: number;
  created_at: Date;
  updated_at: Date;
  totalPaymentPaid: number;
  upcomingPayment: number;
  overduePayment: number;
  gameActivityCount: number;
  transactionsCount: number;
  transactions: {
    id: string;
    transactionId: string;
    date: string;
    paidByReceivedFrom: string;
    paidTo: string;
    type: string;
    amount: number;
    status: string;
  }[];
}

// Interface for importing referees
export interface ImportableReferee {
  id: number;
  added_by: number;
  name: string;
  email: string;
  contact: string;
  department?: string;
  position?: string;
  avatar?: number;
  role?: 'Office Staff' | 'Referee' | 'Other Staff';
}

export interface ImportRefereesResult {
  success: boolean;
  imported: number;
  failed: number;
  message: string;
  errors?: string[];
}

/**
 * Get current user's referee members from secondary database
 * @param userId - User ID (from JWT token)
 * @param parentUserId - Parent User ID (organizer ID from JWT token)
 * @returns Promise<RefereeMember[]>
 */
export async function getCurrentUserReferees(userId: number, parentUserId?: number): Promise<RefereeMember[]> {
  try {
    // Use parentUserId if available, otherwise fall back to userId
    const organizerId = parentUserId || userId;

    const referees = await executeSecondaryQuery<RefereeMember>(
      `SELECT DISTINCT 
    u.id,
    u.added_by,
    CONCAT(COALESCE(u.firstname, ''), ' ', COALESCE(u.lastname, '')) AS name,
    u.email,
    u.contact_number AS contact,
    CASE 
      WHEN u.profile_pic IS NOT NULL AND u.profile_pic != '' 
      THEN u.profile_pic 
      ELSE CONCAT('https://ui-avatars.com/api/?name=', REPLACE(CONCAT(COALESCE(u.firstname, ''), ' ', COALESCE(u.lastname, '')), ' ', '+'))
    END AS avatar,
    r.role_name AS department,
    u.created_at,
    u.updated_at
FROM 
    users u
LEFT JOIN 
    user_roles ur ON u.id = ur.user_id
LEFT JOIN 
    roles r ON ur.role_id = r.id
LEFT JOIN 
    organizer_members om ON u.id = om.member_id AND om.organizer_user_id = ?
WHERE 
    (u.added_by = ? OR om.organizer_user_id = ?)
    AND u.enabled = 1
    AND u.status_id NOT IN (3, 28)
    AND ur.role_id = 10;`,  // Only role_id 10 for referees
      [organizerId, organizerId, organizerId]
    );

    logger.info('Referee members retrieved for user', { userId, count: referees.length });
    return referees;
  } catch (error) {
    logger.error('Error fetching user referees', { userId, error });
    throw error;
  }
}

/**
 * Get referee member's transaction history with pagination and filtering
 * @param userId - User ID
 * @param options - Query options for pagination and filtering
 * @returns Promise<{transactions: any[], total: number, hasMore: boolean}>
 */
export async function getRefereeTransactionHistory(
  userId: number,
  options: {
    limit?: number;
    offset?: number;
    search?: string;
    type?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
  } = {}
): Promise<{transactions: any[], total: number, hasMore: boolean}> {
  try {
    const {
      limit = 20,
      offset = 0,
      search = '',
      type = '',
      status = '',
      dateFrom = '',
      dateTo = ''
    } = options;

    logger.info('Fetching referee transaction history', {
      userId,
      userIdType: typeof userId,
      options
    });

    // Build WHERE conditions - user_id is bigint in database
    let whereConditions = ['wt.user_id = ?'];
    let queryParams: any[] = [userId];

    // Add search filter
    if (search) {
      whereConditions.push('(wt.description LIKE ? OR wt.reference_id LIKE ?)');
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // Add type filter (payment_provider)
    if (type && type !== 'all') {
      whereConditions.push('wt.payment_provider = ?');
      queryParams.push(type);
    }

    // Add status filter - status_id is varchar(50) in database
    if (status && status !== 'all') {
      const statusMap: { [key: string]: string } = {
        'paid': '1',
        'pending': '2',
        'failed': '3'
      };
      if (statusMap[status]) {
        whereConditions.push('wt.status_id = ?');
        queryParams.push(statusMap[status]);
      }
    }

    // Add date range filters
    if (dateFrom) {
      whereConditions.push('DATE(wt.created_at) >= ?');
      queryParams.push(dateFrom);
    }
    if (dateTo) {
      whereConditions.push('DATE(wt.created_at) <= ?');
      queryParams.push(dateTo);
    }

    const whereClause = whereConditions.join(' AND ');

    logger.info('Built WHERE clause for referee transactions', {
      userId,
      whereClause,
      queryParams,
      whereConditions
    });

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM wallet_transactions wt
      WHERE ${whereClause}
    `;

    const countResult = await executeQuerySingle<{total: number}>(countQuery, queryParams);
    const total = countResult?.total || 0;

    // Get transactions with pagination
    const transactionsQuery = `
      SELECT 
        wt.id,
        wt.transaction_id as transactionId,
        DATE_FORMAT(wt.created_at, '%Y-%m-%d %H:%i:%s') as date,
        wt.description,
        wt.amount,
        wt.transaction_type as type,
        wt.payment_provider,
        wt.reference_id,
        CASE 
          WHEN wt.status_id = '1' THEN 'completed'
          WHEN wt.status_id = '2' THEN 'pending'
          WHEN wt.status_id = '3' THEN 'failed'
          ELSE 'unknown'
        END as status,
        wt.created_at,
        wt.updated_at
      FROM wallet_transactions wt
      WHERE ${whereClause}
      ORDER BY wt.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const transactions = await executeQuery(transactionsQuery, [...queryParams, limit, offset]);

    const hasMore = (offset + limit) < total;

    logger.info('Referee transaction history retrieved', {
      userId,
      total,
      returned: transactions.length,
      hasMore,
      limit,
      offset
    });

    return {
      transactions,
      total,
      hasMore
    };

  } catch (error) {
    logger.error('Error fetching referee transaction history', {
      userId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Get referee details from secondary database
 * @param userId - User ID
 * @returns Promise<RefereeDetails | null>
 */
export async function getRefereeDetails(userId: number): Promise<RefereeDetails | null> {
  try {
    const userDetails = await executeSecondaryQuerySingle<RefereeDetails>(
      `SELECT
        u.id,
        CONCAT(COALESCE(u.firstname, ''), ' ', COALESCE(u.lastname, '')) AS name,
        u.contact_number AS contact,
        r.role_name AS department,
        u.position,
        u.profile_pic AS avatar,
        u.created_at,
        u.updated_at,
        0 as totalPaymentPaid,
        0 as upcomingPayment,
        0 as overduePayment,
        0 as gameActivityCount,
        0 as transactionsCount
      FROM users u
      LEFT JOIN user_roles ur ON u.id = ur.user_id
      LEFT JOIN roles r ON ur.role_id = r.id
      WHERE u.id = ? AND ur.role_id = 10`,  // Only role_id 10 for referees
      [userId]
    );

    if (userDetails) {
      // Add empty transactions array
      userDetails.transactions = [];
    }

    return userDetails;
  } catch (error) {
    logger.error('Error fetching referee details', { userId, error });
    throw error;
  }
}

/**
 * Get referee's department information
 * @param userId - User ID
 * @returns Promise<any>
 */
export async function getRefereeDepartment(userId: number): Promise<any> {
  try {
    const department = await executeSecondaryQuerySingle(
      `SELECT r.role_name AS department
      FROM users u
      LEFT JOIN user_roles ur ON u.id = ur.user_id
      LEFT JOIN roles r ON ur.role_id = r.id
      WHERE u.id = ? AND ur.role_id = 10`,  // Only role_id 10 for referees
      [userId]
    );

    return department;
  } catch (error) {
    logger.error('Error fetching referee department', { userId, error });
    throw error;
  }
}

/**
 * Get referee's manager information
 * @param userId - User ID
 * @returns Promise<any>
 */
export async function getRefereeManager(userId: number): Promise<any> {
  try {
    // For referees, manager would typically be the organizer
    const manager = await executeSecondaryQuerySingle(
      `SELECT
        org.id,
        CONCAT(COALESCE(org.firstname, ''), ' ', COALESCE(org.lastname, '')) AS name,
        org.email,
        org.contact_number AS contact
      FROM users u
      LEFT JOIN users org ON u.added_by = org.id
      WHERE u.id = ?`,
      [userId]
    );

    return manager;
  } catch (error) {
    logger.error('Error fetching referee manager', { userId, error });
    throw error;
  }
}

/**
 * Get referee's team members
 * @param userId - User ID
 * @returns Promise<any[]>
 */
export async function getRefereeTeamMembers(userId: number): Promise<any[]> {
  try {
    const teamMembers = await executeSecondaryQuery(
      `SELECT DISTINCT
        u.id,
        CONCAT(COALESCE(u.firstname, ''), ' ', COALESCE(u.lastname, '')) AS name,
        u.email,
        u.contact_number AS contact,
        r.role_name AS role
      FROM users u
      LEFT JOIN user_roles ur ON u.id = ur.user_id
      LEFT JOIN roles r ON ur.role_id = r.id
      WHERE u.added_by = (SELECT added_by FROM users WHERE id = ?)
        AND ur.role_id = 10`,  // Only role_id 10 for referees
      [userId]
    );

    return teamMembers;
  } catch (error) {
    logger.error('Error fetching referee team members', { userId, error });
    throw error;
  }
}

/**
 * Get referee's projects
 * @param userId - User ID
 * @returns Promise<any[]>
 */
export async function getRefereeProjects(userId: number): Promise<any[]> {
  try {
    // For referees, projects might be games/matches they're assigned to
    const projects = await executeSecondaryQuery(
      `SELECT
        'referee_assignments' as type,
        'Game Assignments' as name,
        'Active referee assignments' as description
      FROM users u
      WHERE u.id = ?`,
      [userId]
    );

    return projects;
  } catch (error) {
    logger.error('Error fetching referee projects', { userId, error });
    throw error;
  }
}

/**
 * Get referee's permissions
 * @param userId - User ID
 * @returns Promise<any[]>
 */
export async function getRefereePermissions(userId: number): Promise<any[]> {
  try {
    const permissions = await executeSecondaryQuery(
      `SELECT
        r.role_name as permission,
        'referee' as type
      FROM users u
      LEFT JOIN user_roles ur ON u.id = ur.user_id
      LEFT JOIN roles r ON ur.role_id = r.id
      WHERE u.id = ? AND ur.role_id = 10`,  // Only role_id 10 for referees
      [userId]
    );

    return permissions;
  } catch (error) {
    logger.error('Error fetching referee permissions', { userId, error });
    throw error;
  }
}

/**
 * Get comprehensive referee information
 * @param userId - User ID
 * @param parentUserId - Parent User ID (optional)
 * @returns Promise<any>
 */
export async function getComprehensiveRefereeInfo(userId: number, parentUserId?: number): Promise<any> {
  try {
    const [details, department, manager, teamMembers, projects, permissions] = await Promise.all([
      getRefereeDetails(userId),
      getRefereeDepartment(userId),
      getRefereeManager(userId),
      getRefereeTeamMembers(userId),
      getRefereeProjects(userId),
      getRefereePermissions(userId)
    ]);

    return {
      details,
      department,
      manager,
      teamMembers,
      projects,
      permissions
    };
  } catch (error) {
    logger.error('Error fetching comprehensive referee info', { userId, error });
    throw error;
  }
}

/**
 * Import multiple users as referee members
 * @param users - Array of users to import
 * @param organizerId - Organizer ID (parent_user_id)
 * @param defaultRole - Default role for imported users
 * @returns Promise<ImportRefereesResult>
 */
export async function importRefereeUsers(
  users: ImportableReferee[],
  organizerId: number,
  defaultRole: 'Staff' | 'Referee'
): Promise<ImportRefereesResult> {
  try {
    let imported = 0;
    let failed = 0;
    const errors: string[] = [];

    logger.info('Starting referee import process', {
      organizerId,
      userCount: users.length,
      defaultRole
    });

    for (const user of users) {
      try {
        // Check if user already exists in secondary database
        const existingUser = await executeSecondaryQuerySingle(
          'SELECT id FROM users WHERE email = ? OR id = ?',
          [user.email, user.id]
        );

        if (existingUser) {
          // Check if user is already associated with this organizer
          const existingAssociation = await executeSecondaryQuerySingle(
            'SELECT id FROM organizer_members WHERE member_id = ? AND organizer_user_id = ?',
            [existingUser.id, organizerId]
          );

          if (!existingAssociation) {
            // Add association to organizer_members table
            await executeUpdate(
              `INSERT INTO organizer_members (organizer_user_id, member_id, role, created_at, updated_at)
               VALUES (?, ?, ?, NOW(), NOW())`,
              [organizerId, existingUser.id, defaultRole || 'Referee']
            );

            // Ensure user has referee role (role_id = 10)
            const existingRole = await executeSecondaryQuerySingle(
              'SELECT id FROM user_roles WHERE user_id = ? AND role_id = 10',
              [existingUser.id]
            );

            if (!existingRole) {
              await executeUpdate(
                'INSERT INTO user_roles (user_id, role_id, created_at, updated_at) VALUES (?, 10, NOW(), NOW())',
                [existingUser.id]
              );
            }

            imported++;
            logger.info('Associated existing user as referee', {
              userId: existingUser.id,
              email: user.email,
              organizerId
            });
          } else {
            errors.push(`User ${user.email} is already associated with this organizer`);
            failed++;
          }
        } else {
          // Create new user in secondary database
          const insertResult = await executeUpdate(
            `INSERT INTO users (
              id, added_by, firstname, lastname, email, contact_number,
              department, position, profile_pic, enabled, status_id, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, 1, NOW(), NOW())`,
            [
              user.id,
              organizerId,
              user.name.split(' ')[0] || '',
              user.name.split(' ').slice(1).join(' ') || '',
              user.email,
              user.contact || '',
              user.department || '',
              user.position || '',
              user.avatar || null
            ]
          );

          if (insertResult.affectedRows > 0) {
            // Add user role (referee = role_id 10)
            await executeUpdate(
              'INSERT INTO user_roles (user_id, role_id, created_at, updated_at) VALUES (?, 10, NOW(), NOW())',
              [user.id]
            );

            // Add to organizer_members
            await executeUpdate(
              `INSERT INTO organizer_members (organizer_user_id, member_id, role, created_at, updated_at)
               VALUES (?, ?, ?, NOW(), NOW())`,
              [organizerId, user.id, defaultRole || 'Referee']
            );

            imported++;
            logger.info('Created new referee user', {
              userId: user.id,
              email: user.email,
              organizerId
            });
          } else {
            errors.push(`Failed to create user ${user.email}`);
            failed++;
          }
        }
      } catch (userError) {
        logger.error('Error importing individual referee user', {
          user,
          error: userError instanceof Error ? userError.message : 'Unknown error'
        });
        errors.push(`Error importing ${user.email}: ${userError instanceof Error ? userError.message : 'Unknown error'}`);
        failed++;
      }
    }

    const success = failed === 0;
    const message = success
      ? `Successfully imported ${imported} referee(s)`
      : `Imported ${imported} referee(s), failed ${failed}`;

    logger.info('Referee import process completed', {
      organizerId,
      imported,
      failed,
      success,
      errors: errors.length
    });

    return {
      success,
      imported,
      failed,
      message,
      errors: errors.length > 0 ? errors : undefined
    };

  } catch (error) {
    logger.error('Error in referee import process', {
      organizerId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      imported: 0,
      failed: users.length,
      message: 'Failed to import referees',
      errors: [error instanceof Error ? error.message : 'Unknown error']
    };
  }
}

// Add referee bank account
export async function addRefereeBank(
  refereeId: string,
  bankAccountData: {
    bankName: string;
    accountNumber: string;
    routingNumber: string;
    accountType: string;
    accountHolderName: string;
  },
  organizerId: number
) {
  try {
    logger.info('Adding referee bank account', { refereeId, organizerId });

    const result = await executeUpdate(
      `INSERT INTO tbl_referee_bank_accounts
       (referee_id, organizer_id, bank_name, account_number, routing_number,
        account_type, account_holder_name, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())`,
      [
        refereeId,
        organizerId,
        bankAccountData.bankName,
        bankAccountData.accountNumber,
        bankAccountData.routingNumber,
        bankAccountData.accountType,
        bankAccountData.accountHolderName
      ]
    );

    if (result.affectedRows > 0) {
      logger.info('Referee bank account added successfully', { refereeId, organizerId });
      return { success: true, message: 'Bank account added successfully', accountId: result.insertId };
    }

    return { success: false, message: 'Failed to add bank account' };
  } catch (error) {
    logger.error('Error adding referee bank account', { refereeId, organizerId, error });
    return { success: false, message: 'Failed to add bank account' };
  }
}

// Update referee bank account
export async function updateRefereeBank(
  accountId: number,
  bankAccountData: {
    bankName: string;
    accountNumber: string;
    routingNumber: string;
    accountType: string;
    accountHolderName: string;
  },
  organizerId: number
) {
  try {
    logger.info('Updating referee bank account', { accountId, organizerId });

    const result = await executeUpdate(
      `UPDATE tbl_referee_bank_accounts
       SET bank_name = ?, account_number = ?, routing_number = ?,
           account_type = ?, account_holder_name = ?, updated_at = NOW()
       WHERE id = ? AND organizer_id = ?`,
      [
        bankAccountData.bankName,
        bankAccountData.accountNumber,
        bankAccountData.routingNumber,
        bankAccountData.accountType,
        bankAccountData.accountHolderName,
        accountId,
        organizerId
      ]
    );

    if (result.affectedRows > 0) {
      logger.info('Referee bank account updated successfully', { accountId, organizerId });
      return { success: true, message: 'Bank account updated successfully' };
    }

    return { success: false, message: 'Bank account not found or no changes made' };
  } catch (error) {
    logger.error('Error updating referee bank account', { accountId, organizerId, error });
    return { success: false, message: 'Failed to update bank account' };
  }
}

// Enhanced transferToRefereeBank function
export async function transferAmountToReferee(
  refereeId: string,
  amount: number,
  description: string,
  organizerId: number,
  paymentSource: 'wallet' | 'bank' = 'wallet',
  pin?: string,
  bankAccountId?: string
): Promise<{ success: boolean; message: string; transactionId?: string }> {
  try {
    logger.info('Starting referee transfer', {
      refereeId,
      amount,
      organizerId,
      paymentSource
    });

    // Validate amount
    if (amount <= 0 || amount > 10000) {
      return { success: false, message: 'Invalid amount. Must be between $1 and $10,000' };
    }

    // If paying from wallet, verify PIN and check balance
    if (paymentSource === 'wallet') {
      if (!pin) {
        return { success: false, message: 'PIN is required for wallet transfers' };
      }

      // Get wallet to check PIN
      const wallet = await getUserMasterWallet(organizerId);
      if (!wallet || !wallet.wallet_master_pin) {
        return { success: false, message: 'Wallet PIN not set for organizer' };
      }

      // Verify PIN
      const pinValid = await comparePin(pin, wallet.wallet_master_pin);
      if (!pinValid) {
        return { success: false, message: 'Invalid PIN' };
      }

      // Check wallet balance
      if (wallet.balance < amount) {
        return { success: false, message: 'Insufficient wallet balance' };
      }
    }

    // Create wallet transaction record
    const transactionResult = await createWalletTransaction(
      organizerId,
      -amount, // Negative for outgoing transfer
      description || `Transfer to referee ${refereeId}`,
      'referee_transfer',
      `REF_${refereeId}_${Date.now()}`,
      paymentSource,
      undefined, // metaData
      '2' // statusId: Pending
    );

    if (transactionResult) {
      logger.info('Referee transfer transaction created', {
        refereeId,
        amount,
        organizerId,
        transactionId: transactionResult
      });

      return {
        success: true,
        message: 'Transfer initiated successfully',
        transactionId: transactionResult.toString()
      };
    }

    return { success: false, message: 'Failed to create transfer transaction' };

  } catch (error) {
    logger.error('Error in referee transfer', {
      refereeId,
      amount,
      organizerId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return { success: false, message: 'Transfer failed due to system error' };
  }
}

// Get referee bank accounts for specific organizer
export async function getRefereeBank(organizerId: number) {
  try {
    const accounts = await executeQuery(
      `SELECT rba.*, u.full_name as referee_name, u.team_connect_user_id as referee_id,
              org.full_name as organizer_name
       FROM tbl_referee_bank_accounts rba
       LEFT JOIN tbl_users u ON rba.referee_id = u.team_connect_user_id
       LEFT JOIN tbl_users org ON rba.organizer_id = org.team_connect_user_id
       WHERE rba.organizer_id = ? AND rba.status = 'active'
       ORDER BY rba.created_at DESC`,
      [organizerId]
    );

    logger.info('Retrieved referee bank accounts', {
      organizerId,
      count: accounts.length
    });

    return accounts;
  } catch (error) {
    logger.error('Error fetching referee bank accounts', { organizerId, error });
    return [];
  }
}

export async function getRefereeBanks(organizerId: number, refereeId: number) {
  try {
    const accounts = await executeQuery(
      `SELECT rba.*, u.full_name as referee_name, u.team_connect_user_id as referee_id,
              org.full_name as organizer_name
       FROM tbl_referee_bank_accounts rba
       LEFT JOIN tbl_users u ON rba.referee_id = u.team_connect_user_id
       LEFT JOIN tbl_users org ON rba.organizer_id = org.team_connect_user_id
       WHERE rba.organizer_id = ? AND rba.referee_id = ? AND rba.status = 'active'
       ORDER BY rba.created_at DESC`,
      [organizerId, refereeId]
    );

    logger.info('Retrieved referee bank accounts for specific referee', {
      organizerId,
      refereeId,
      count: accounts.length
    });

    return accounts;
  } catch (error) {
    logger.error('Error fetching referee bank accounts for specific referee', {
      organizerId,
      refereeId,
      error
    });
    return [];
  }
}
